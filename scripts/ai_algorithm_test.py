#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 算法测试脚本
通过工厂模式创建客户端，并使用图片测试算法
"""

import sys
import asyncio
import json
import base64
from pathlib import Path
from typing import Optional

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    # 导入 AI SDK 相关模块
    from ai_sdk.sdk_enter import SensoroFactory, SensoroClientType
    from ai_sdk.helpers import SAIWholeTargetDetectLatest
    from ai_sdk.settings import CntEnvConstant
except ImportError as e:
    print(f"❌ 错误: 未找到 AI SDK 模块: {e}")
    print("请确保 ai_sdk 目录存在于项目根目录")
    sys.exit(1)

try:
    # 导入 S3 SDK
    from s3_sdk.base import S3BaseClient
    S3_AVAILABLE = True
except ImportError:
    print("⚠️  警告: 未找到 S3 SDK，将使用 base64 编码进行测试")
    S3_AVAILABLE = False

from config import DOWNLOAD_FOLDER, ALGORITHM_OUTPUT_DIR
from settings import CntEnvConstant


class AIAlgorithmTester:
    def __init__(self, use_s3_url=True, use_base64=False):
        self.download_path = Path(DOWNLOAD_FOLDER)
        self.algorithm_results_dir = Path(ALGORITHM_OUTPUT_DIR)
        self.use_s3_url = use_s3_url and S3_AVAILABLE  # 是否使用 S3 预签名 URL
        self.use_base64 = use_base64  # 是否使用 base64 编码本地图片
        self.setup_ai_client()
        self.setup_s3_client()
        self.setup_log_file()

    def setup_ai_client(self):
        """通过工厂模式初始化AI客户端"""
        try:
            # 使用工厂模式创建客户端，指定参数初始化
            self.ai_client = SensoroFactory(SensoroClientType.x).create_client(
                app_id=CntEnvConstant.SENSORO_OPENAI_API_ID,
                app_secret=CntEnvConstant.SENSORO_OPENAI_API_SK,
                host=CntEnvConstant.SENSORO_OPENAI_API_HOST,
            )

            print(f"✅ AI客户端初始化成功")
            print(f"   - 客户端类型: {SensoroClientType.x}")
            print(f"   - API Host: {CntEnvConstant.SENSORO_OPENAI_API_HOST}")
            print(f"   - App ID: {CntEnvConstant.SENSORO_OPENAI_API_ID[:10]}...")

        except Exception as e:
            print(f"❌ AI客户端初始化失败: {str(e)}")
            sys.exit(1)

    def setup_s3_client(self):
        """初始化S3客户端（如果启用）"""
        if self.use_s3_url:
            try:
                self.s3_client = S3BaseClient(
                    bucket_name=CntEnvConstant.S3_PUBLIC_BUCKET_NAME,
                    access_key=CntEnvConstant.S3_PUBLIC_ACCESS_KEY,
                    secret_key=CntEnvConstant.S3_PUBLIC_SECRET_KEY,
                    region_name=CntEnvConstant.S3_PUBLIC_REGEION,
                    public_endpoint_url=CntEnvConstant.S3_PUBLIC_REGEION_HOST_URL,
                    private_endpoint_url=CntEnvConstant.S3_PUBLIC_REGEION_HOST_URL,
                    sign_style=CntEnvConstant.S3_PUBLIC_SIGN_STYLE,
                    signature_version=CntEnvConstant.S3_PUBLIC_SIGN_VERSION,
                )
                print(f"✅ S3客户端初始化成功")
                print(f"   - 存储桶: {CntEnvConstant.S3_PUBLIC_BUCKET_NAME}")
                print(f"   - 上传前缀: {CntEnvConstant.S3_UPLOAD_PREFIX}")
            except Exception as e:
                print(f"⚠️  S3客户端初始化失败: {str(e)}")
                print("   - 将使用 base64 编码进行测试")
                self.use_s3_url = False
                self.use_base64 = True
        else:
            self.s3_client = None
    
    def setup_log_file(self):
        """设置算法结果目录"""
        # 创建算法响应结果目录
        self.algorithm_results_dir = self.algorithm_results_dir
        self.algorithm_results_dir.mkdir(exist_ok=True)
    
    async def _generate_s3_presigned_url(self, image_path: Path) -> Optional[str]:
        """为本地图片生成 S3 预签名 URL"""
        if not self.s3_client:
            return None

        try:
            # 构建 S3 对象键，基于 S3_UPLOAD_PREFIX 和文件名
            s3_key = f"{CntEnvConstant.S3_UPLOAD_PREFIX}/{image_path.name}"

            # 生成预签名 URL
            presigned_url = await self.s3_client.generate_pre_signed_url(
                s3_key=s3_key,
                expires_in=3600,  # 1小时有效期
                method="GET"
            )

            return presigned_url

        except Exception as e:
            print(f"❌ 生成预签名URL失败: {str(e)}")
            return None

    def _encode_image_to_base64(self, image_path: Path) -> Optional[str]:
        """将图片文件编码为 base64 字符串"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_string = base64.b64encode(image_data).decode('utf-8')
                return base64_string
        except Exception as e:
            print(f"❌ 图片编码失败: {str(e)}")
            return None

    def _save_algorithm_result(self, image_path, result):
        """为每张图片保存单独的原始算法响应JSON文件"""
        if result:
            # 获取图片文件名（不含路径和扩展名）
            image_filename = Path(image_path).stem
            # 创建与图片同名的JSON文件
            result_file_path = self.algorithm_results_dir / f"{image_filename}.json"

            # 保存原始算法响应
            with open(result_file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            print(f"   💾 已保存算法响应: {result_file_path.name}")
    
    async def test_image_with_algorithm(self, image_path: Path, image_url: Optional[str] = None):
        """使用算法测试单张图片"""
        print(f"🔍 测试图片: {image_path.name}")

        try:
            if image_url:
                # 使用提供的图片URL
                print(f"   使用提供的图片URL进行测试")
                result = await SAIWholeTargetDetectLatest.detect(
                    image_url=image_url,
                    enable_multiple=True,
                    ai_client=self.ai_client
                )

            elif self.use_s3_url and image_path.exists():
                # 使用 S3 预签名 URL
                print(f"   生成 S3 预签名 URL 进行测试")
                presigned_url = await self._generate_s3_presigned_url(image_path)
                if presigned_url:
                    print(f"   预签名URL: {presigned_url[:80]}...")
                    result = await SAIWholeTargetDetectLatest.detect(
                        image_url=presigned_url,
                        enable_multiple=True,
                        ai_client=self.ai_client
                    )
                else:
                    raise Exception("生成 S3 预签名 URL 失败")

            elif self.use_base64 and image_path.exists():
                # 使用 base64 编码本地图片
                print(f"   使用 base64 编码进行测试")
                base64_data = self._encode_image_to_base64(image_path)
                if base64_data:
                    result = await SAIWholeTargetDetectLatest.detect(
                        image_base64=base64_data,
                        enable_multiple=True,
                        ai_client=self.ai_client
                    )
                else:
                    raise Exception("图片 base64 编码失败")
            else:
                # 使用示例URL进行演示
                print("⚠️  使用示例URL进行演示测试")
                result = await SAIWholeTargetDetectLatest.detect(
                    image_url="https://aise-dev1.tos-s3-cn-beijing.volces.com/DMS/6733595785906806784/upload/64be3b184f6968d45600526a.jpg",
                    enable_multiple=True,
                    ai_client=self.ai_client
                )

            # 解析检测结果
            item_count = result.get('item_count', 0)
            items = result.get('items', [])

            print(f"✅ 检测成功: 发现 {item_count} 个目标")

            # 显示检测到的目标详情
            if items:
                print("   检测到的目标:")
                for i, item in enumerate(items[:5], 1):  # 只显示前5个
                    target_type = item.get('type', 'unknown')
                    score = item.get('score', 0)
                    location = item.get('location', {})
                    print(f"     {i}. 类型: {target_type}, 置信度: {score:.3f}, 位置: {location}")

                if len(items) > 5:
                    print(f"     ... 还有 {len(items) - 5} 个目标")

            # 保存算法响应结果
            self._save_algorithm_result(image_path, result)
            return True, result

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 检测失败: {error_msg}")
            return False, None
    
    def get_sample_images(self, max_count: int = 5):
        """获取样本图片进行测试"""
        if not self.download_path.exists():
            print(f"❌ 下载目录不存在: {self.download_path}")
            return []
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        
        image_files = []
        for file_path in self.download_path.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(file_path)
                if len(image_files) >= max_count:
                    break
        
        return sorted(image_files)
    
    async def run_batch_test(self, max_images: int = 5):
        """运行批量测试"""
        print(f"🚀 开始AI算法批量测试")
        print(f"📁 图片目录: {self.download_path.absolute()}")
        print(f"🤖 算法类型: 自研全目标检测 (Latest)")
        print(f"🔢 最大测试数量: {max_images}")
        print("-" * 50)
        
        # 获取样本图片
        sample_images = self.get_sample_images(max_images)
        
        if not sample_images:
            print("❌ 没有找到图片文件进行测试")
            return
        
        print(f"📊 找到 {len(sample_images)} 个图片文件进行测试")
        
        success_count = 0
        failed_count = 0
        
        # 逐个测试图片
        for i, image_path in enumerate(sample_images, 1):
            print(f"\n[{i}/{len(sample_images)}] ", end="")
            success, result = await self.test_image_with_algorithm(image_path)
            
            if success:
                success_count += 1
            else:
                failed_count += 1
        
        # 输出统计信息
        print("\n" + "=" * 50)
        print(f"🎉 AI算法测试完成！")
        print(f"📊 统计信息:")
        print(f"   - 测试成功: {success_count} 张图片")
        print(f"   - 测试失败: {failed_count} 张图片")
        print(f"   - 总计图片: {len(sample_images)} 张")
        print(f"   - 成功率: {success_count/len(sample_images)*100:.1f}%")
        print(f"   - 算法响应文件: {self.algorithm_results_dir.absolute()}")
    
    async def test_single_image_url(self, image_url: str):
        """测试单个图片URL"""
        print(f"🚀 开始AI算法单图测试")
        print(f"🖼️  图片URL: {image_url}")
        print(f"🤖 算法类型: 自研全目标检测 (Latest)")
        print("-" * 50)
        
        # 创建虚拟路径用于日志记录
        virtual_path = Path(image_url.split('/')[-1])
        
        success, result = await self.test_image_with_algorithm(virtual_path, image_url)

        print("\n" + "=" * 50)
        if success:
            print(f"🎉 AI算法测试成功！")
            print(f"   - 算法响应文件: {self.algorithm_results_dir.absolute()}")
        else:
            print(f"❌ AI算法测试失败！")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='AI算法测试脚本')
    parser.add_argument('--url', type=str, help='测试单个图片URL')
    parser.add_argument('--batch', type=int, default=5, help='批量测试图片数量 (默认: 5)')
    parser.add_argument('--method', type=str, choices=['s3', 'base64', 'demo'],
                       default='s3', help='测试方法: s3=S3预签名URL, base64=Base64编码, demo=示例URL (默认: s3)')
    parser.add_argument('--no-s3', action='store_true', help='禁用S3预签名URL，使用base64编码')

    args = parser.parse_args()

    # 根据参数设置测试方法
    if args.no_s3 or args.method == 'base64':
        use_s3_url = False
        use_base64 = True
    elif args.method == 'demo':
        use_s3_url = False
        use_base64 = False
    else:  # args.method == 's3' (默认)
        use_s3_url = True
        use_base64 = False

    tester = AIAlgorithmTester(use_s3_url=use_s3_url, use_base64=use_base64)

    if args.url:
        # 测试单个图片URL
        asyncio.run(tester.test_single_image_url(args.url))
    else:
        # 批量测试本地图片
        asyncio.run(tester.run_batch_test(args.batch))


if __name__ == "__main__":
    main()
