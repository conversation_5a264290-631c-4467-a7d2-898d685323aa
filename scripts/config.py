# -*- coding: utf-8 -*-
import os
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

# API 配置
API_BASE_URL = "https://ai-api.xn.sensoro.vip"
API_ENDPOINT = "/api/daas/v1/body/queryBodies"
AUTHORIZATION_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJuYmYiOjE3NTM0MTAwOTQsImV4cCI6MTc1MzQ1MzI5NCwiaWF0IjoxNzUzNDEwMDk0LCJ1c2VyX2lkIjo4NSwidXNlcm5hbWUiOiJzemN4X3FzenlrXzE3MjUwNzc0NjU2MjQ2Nzg0MDEiLCJ0ZW5hbnRfaWQiOjE3MjUwNzc0NjU2MjQ2Nzg0MDEsImNsaWVudF90eXBlIjo0LCJzZXJ2aWNlX3R5cGUiOiJpdm1zIn0.isMdYBZAIgNvUFQDnx4czYucSztLbQcLtgAmGuCXkXed8e9kbSqLAPQ1MZFUJMAz8NfJ7-fmjmqnBE5BGtCLvg"

# 下载配置
DOWNLOAD_FOLDER = "../downloaded_images"
ALGORITHM_OUTPUT_DIR = "../algorithm_results"
CROPPED_OUTPUT_DIR = "../cropped_humans"
MAX_IMAGES = 1000
CONCURRENT_DOWNLOADS = 10  # 并发下载数量
TEST_MODE = False  # 测试模式，设为 True 时只下载少量图片进行测试

