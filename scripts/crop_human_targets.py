#!/usr/bin/env python3
"""
人体目标裁剪脚本

解析 algorithm_results 目录下的 AI 算法结果 JSON 文件，
提取 type 为 "human" 的目标，根据 location 坐标并外扩 1.1 倍，
在原图上裁剪对应目标并保存为小图片。

小图片命名格式：大图文件名_{数字}.jpg
"""

import sys
import json
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import math

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from PIL import Image
    print("✅ PIL (Pillow) 库加载成功")
except ImportError:
    print("❌ 缺少 PIL (Pillow) 库，请安装: pip install Pillow")
    sys.exit(1)


class HumanTargetCropper:
    """人体目标裁剪器"""
    
    def __init__(self, downloaded_images_dir: str = "downloaded_images", 
                 algorithm_results_dir: str = "algorithm_results",
                 cropped_output_dir: str = "cropped_humans"):
        """
        初始化裁剪器
        
        Args:
            downloaded_images_dir: 原始图片目录
            algorithm_results_dir: AI算法结果JSON文件目录  
            cropped_output_dir: 裁剪后图片输出目录
        """
        self.project_root = Path(__file__).parent.parent
        self.downloaded_images_dir = self.project_root / downloaded_images_dir
        self.algorithm_results_dir = self.project_root / algorithm_results_dir
        self.cropped_output_dir = self.project_root / cropped_output_dir
        
        # 创建输出目录
        self.cropped_output_dir.mkdir(exist_ok=True)
        
        print(f"📁 原始图片目录: {self.downloaded_images_dir.absolute()}")
        print(f"📁 算法结果目录: {self.algorithm_results_dir.absolute()}")
        print(f"📁 裁剪输出目录: {self.cropped_output_dir.absolute()}")
    
    def get_image_filename_from_json(self, json_filename: str) -> str:
        """从JSON文件名推导出对应的图片文件名"""
        # JSON文件名格式: xxx.json -> 图片文件名格式: xxx.jpg
        return json_filename.replace('.json', '.jpg')
    
    def expand_bbox(self, bbox: Dict, image_width: int, image_height: int, 
                   expand_ratio: float = 1.1) -> Tuple[int, int, int, int]:
        """
        扩展边界框
        
        Args:
            bbox: 边界框字典 {"left": x, "top": y, "width": w, "height": h}
            image_width: 图片宽度
            image_height: 图片高度
            expand_ratio: 扩展比例 (1.1 表示扩展10%)
            
        Returns:
            (x1, y1, x2, y2) 扩展后的边界框坐标
        """
        left = bbox["left"]
        top = bbox["top"]
        width = bbox["width"]
        height = bbox["height"]
        
        # 计算中心点
        center_x = left + width / 2
        center_y = top + height / 2
        
        # 扩展尺寸
        new_width = width * expand_ratio
        new_height = height * expand_ratio
        
        # 计算新的边界框
        x1 = int(center_x - new_width / 2)
        y1 = int(center_y - new_height / 2)
        x2 = int(center_x + new_width / 2)
        y2 = int(center_y + new_height / 2)
        
        # 确保边界框在图片范围内
        x1 = max(0, x1)
        y1 = max(0, y1)
        x2 = min(image_width, x2)
        y2 = min(image_height, y2)
        
        return x1, y1, x2, y2
    
    def crop_human_targets_from_image(self, image_path: Path, json_path: Path) -> int:
        """
        从单张图片中裁剪人体目标
        
        Args:
            image_path: 图片文件路径
            json_path: 对应的JSON结果文件路径
            
        Returns:
            裁剪成功的人体目标数量
        """
        try:
            # 读取JSON结果
            with open(json_path, 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            
            # 筛选出人体目标
            human_items = [item for item in result_data.get('items', [])
                          if item.get('type') == 'human']
            
            if not human_items:
                print(f"   ⚠️  未发现人体目标")
                return 0
            
            # 打开原始图片
            with Image.open(image_path) as img:
                image_width, image_height = img.size
                print(f"   📐 图片尺寸: {image_width}x{image_height}")
                
                cropped_count = 0
                base_filename = image_path.stem  # 不含扩展名的文件名
                
                for i, human_item in enumerate(human_items, 1):
                    location = human_item.get('location', {})
                    score = human_item.get('score', 0)
                    
                    print(f"   👤 人体目标 {i}: 置信度={score:.2f}, "
                          f"位置=({location.get('left', 0)}, {location.get('top', 0)}, "
                          f"{location.get('width', 0)}, {location.get('height', 0)})")
                    
                    # 扩展边界框
                    x1, y1, x2, y2 = self.expand_bbox(location, image_width, image_height)
                    
                    # 检查边界框有效性
                    if x2 <= x1 or y2 <= y1:
                        print(f"   ❌ 无效的边界框: ({x1}, {y1}, {x2}, {y2})")
                        continue
                    
                    print(f"   ✂️  扩展后边界框: ({x1}, {y1}, {x2}, {y2})")
                    
                    # 裁剪图片
                    cropped_img = img.crop((x1, y1, x2, y2))
                    
                    # 生成输出文件名
                    output_filename = f"{base_filename}_{i}.jpg"
                    output_path = self.cropped_output_dir / output_filename
                    
                    # 保存裁剪后的图片
                    cropped_img.save(output_path, 'JPEG', quality=95)
                    cropped_count += 1
                    
                    print(f"   💾 已保存: {output_filename} (尺寸: {x2-x1}x{y2-y1})")
                
                return cropped_count
                
        except Exception as e:
            print(f"   ❌ 处理失败: {str(e)}")
            return 0
    
    def process_all_results(self, max_files: Optional[int] = None) -> Dict[str, int]:
        """
        处理所有算法结果文件
        
        Args:
            max_files: 最大处理文件数量，None表示处理所有文件
            
        Returns:
            处理统计信息
        """
        print(f"🚀 开始批量处理人体目标裁剪")
        print("-" * 50)
        
        # 获取所有JSON文件
        json_files = list(self.algorithm_results_dir.glob("*.json"))
        
        if not json_files:
            print(f"❌ 在 {self.algorithm_results_dir} 中未找到JSON文件")
            return {"processed": 0, "success": 0, "failed": 0, "total_humans": 0}
        
        if max_files:
            json_files = json_files[:max_files]
        
        print(f"📊 找到 {len(json_files)} 个算法结果文件")
        
        stats = {"processed": 0, "success": 0, "failed": 0, "total_humans": 0}
        
        for i, json_path in enumerate(json_files, 1):
            print(f"\n[{i}/{len(json_files)}] 🔍 处理: {json_path.name}")
            
            # 推导对应的图片文件名
            image_filename = self.get_image_filename_from_json(json_path.name)
            image_path = self.downloaded_images_dir / image_filename
            
            stats["processed"] += 1
            
            if not image_path.exists():
                print(f"   ❌ 对应图片不存在: {image_filename}")
                stats["failed"] += 1
                continue
            
            # 裁剪人体目标
            human_count = self.crop_human_targets_from_image(image_path, json_path)
            
            if human_count > 0:
                stats["success"] += 1
                stats["total_humans"] += human_count
                print(f"   ✅ 成功裁剪 {human_count} 个人体目标")
            else:
                print(f"   ⚠️  未裁剪到人体目标")
        
        # 输出统计信息
        print("\n" + "=" * 50)
        print(f"🎉 人体目标裁剪完成！")
        print(f"📊 处理统计:")
        print(f"   - 处理文件: {stats['processed']} 个")
        print(f"   - 成功处理: {stats['success']} 个")
        print(f"   - 处理失败: {stats['failed']} 个")
        print(f"   - 裁剪人体: {stats['total_humans']} 个")
        print(f"   - 输出目录: {self.cropped_output_dir.absolute()}")
        
        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='人体目标裁剪脚本')
    parser.add_argument('--max-files', type=int, help='最大处理文件数量')
    parser.add_argument('--downloaded-dir', type=str, default='downloaded_images',
                       help='原始图片目录 (默认: downloaded_images)')
    parser.add_argument('--results-dir', type=str, default='algorithm_results',
                       help='算法结果目录 (默认: algorithm_results)')
    parser.add_argument('--output-dir', type=str, default='cropped_humans',
                       help='裁剪输出目录 (默认: cropped_humans)')
    
    args = parser.parse_args()
    
    # 创建裁剪器
    cropper = HumanTargetCropper(
        downloaded_images_dir=args.downloaded_dir,
        algorithm_results_dir=args.results_dir,
        cropped_output_dir=args.output_dir
    )
    
    # 处理所有结果
    stats = cropper.process_all_results(max_files=args.max_files)
    
    return 0 if stats["success"] > 0 else 1


if __name__ == "__main__":
    sys.exit(main())
