#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片下载器主入口
提供测试和完整下载选项
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def show_menu():
    """显示菜单"""
    print("=== 图片下载和S3上传工具 ===")
    print("1. 运行测试 (测试 API 连接和下载功能)")
    print("2. 测试重试机制 (测试超时和错误重试)")
    print("3. 测试S3连接 (验证S3配置和权限)")
    print("4. 仅下载图片 (下载 1000 张图片)")
    print("5. 仅上传图片到S3 (上传已下载的图片)")
    print("6. 裁剪人体目标 (从大图全目标算法结果中裁剪人体目标)")
    print("7. 退出")
    print("=" * 30)


def run_test():
    """运行测试"""
    try:
        from test_downloader import main as test_main
        test_main()
    except Exception as e:
        print(f"测试运行出错: {str(e)}")


def run_retry_test():
    """运行重试机制测试"""
    try:
        from test_retry_mechanism import main as retry_test_main
        retry_test_main()
    except Exception as e:
        print(f"重试测试运行出错: {str(e)}")


def run_s3_test():
    """运行S3连接测试"""
    try:
        from test_s3_connection import main as s3_test_main
        s3_test_main()
    except Exception as e:
        print(f"S3测试运行出错: {str(e)}")


def run_download():
    """运行完整下载"""
    try:
        from image_downloader import main as download_main
        print("开始完整下载...")
        download_main()
    except Exception as e:
        print(f"下载运行出错: {str(e)}")


def run_upload():
    """运行S3上传"""
    try:
        from s3_uploader import main as upload_main
        print("开始上传到S3...")
        upload_main()
    except Exception as e:
        print(f"上传运行出错: {str(e)}")


def run_crop_humans():
    """运行人体目标裁剪"""
    try:
        from run_crop_human_targets import HumanTargetCropper
        print("开始裁剪人体目标...")

        # 创建裁剪器实例
        cropper = HumanTargetCropper()

        # 询问是否限制处理文件数量
        max_files_input = input("请输入最大处理文件数量 (直接回车处理所有文件): ").strip()
        max_files = None
        if max_files_input:
            try:
                max_files = int(max_files_input)
                print(f"将处理最多 {max_files} 个文件")
            except ValueError:
                print("输入无效，将处理所有文件")

        # 执行裁剪
        stats = cropper.process_all_results(max_files=max_files)

        # 显示结果
        if stats["success"] > 0:
            print(f"✅ 裁剪完成！成功处理 {stats['success']} 个文件，裁剪 {stats['total_humans']} 个人体目标")
        else:
            print("⚠️  未成功裁剪任何人体目标")

    except Exception as e:
        print(f"人体目标裁剪运行出错: {str(e)}")



def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请选择操作 (1-7): ").strip()

            if choice == '1':
                print("\n" + "="*50)
                run_test()
                print("="*50 + "\n")
                input("按回车键继续...")

            elif choice == '2':
                print("\n" + "="*50)
                print("⚠️  注意：此测试会尝试连接无效URL，可能需要较长时间")
                confirm = input("确定要运行重试机制测试吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_retry_test()
                else:
                    print("取消测试")
                print("="*50 + "\n")
                input("按回车键继续...")

            elif choice == '3':
                print("\n" + "="*50)
                print("测试S3连接和权限...")
                run_s3_test()
                print("="*50 + "\n")
                input("按回车键继续...")

            elif choice == '4':
                print("\n" + "="*50)
                confirm = input("确定要开始下载 1000 张图片吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_download()
                else:
                    print("取消下载")
                print("="*50 + "\n")
                input("按回车键继续...")

            elif choice == '5':
                print("\n" + "="*50)
                confirm = input("确定要上传已下载的图片到S3吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_upload()
                else:
                    print("取消上传")
                print("="*50 + "\n")
                input("按回车键继续...")

            elif choice == '6':
                print("\n" + "="*50)
                print("裁剪人体目标...")
                print("此功能将从算法结果JSON文件中提取人体目标并裁剪保存")
                confirm = input("确定要开始裁剪人体目标吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_crop_humans()
                else:
                    print("取消裁剪")
                print("="*50 + "\n")
                input("按回车键继续...")

            elif choice == '7':
                print("退出程序")
                break

            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n用户中断程序")
            break
        except Exception as e:
            print(f"程序出错: {str(e)}")


if __name__ == "__main__":
    main()
